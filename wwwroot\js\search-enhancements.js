/**
 * Enhanced Search Form Interactions
 * Adds beautiful animations and interactions to the search forms
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeSearchEnhancements();
});

function initializeSearchEnhancements() {
    // Add floating label effects
    addFloatingLabelEffects();
    
    // Add form validation with visual feedback
    addFormValidation();
    
    // Add smooth tab transitions
    addTabTransitions();
    
    // Add input focus animations
    addInputAnimations();
    
    // Add button ripple effects
    addButtonRippleEffects();
    
    // Add loading states
    addLoadingStates();
}

/**
 * Floating label effects for better UX
 */
function addFloatingLabelEffects() {
    const inputGroups = document.querySelectorAll('.search-tabs .input-group');
    
    inputGroups.forEach(group => {
        const input = group.querySelector('.form-control, .form-select');
        const label = group.closest('.col-md-2, .col-md-3, .col-md-4, .col-md-5')?.querySelector('.form-label');
        
        if (input && label) {
            // Add floating effect on focus
            input.addEventListener('focus', () => {
                label.style.transform = 'translateY(-2px)';
                label.style.color = 'rgba(76, 175, 80, 0.9)';
                label.style.textShadow = '0 0 8px rgba(76, 175, 80, 0.3)';
            });
            
            input.addEventListener('blur', () => {
                if (!input.value) {
                    label.style.transform = 'translateY(0)';
                    label.style.color = 'rgba(255, 255, 255, 0.95)';
                    label.style.textShadow = '0 1px 2px rgba(0, 0, 0, 0.1)';
                }
            });
        }
    });
}

/**
 * Form validation with visual feedback
 */
function addFormValidation() {
    const forms = document.querySelectorAll('.search-tabs form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    addErrorState(field);
                } else {
                    removeErrorState(field);
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                showValidationMessage('Vui lòng điền đầy đủ thông tin bắt buộc');
            }
        });
    });
}

function addErrorState(field) {
    const inputGroup = field.closest('.input-group');
    if (inputGroup) {
        inputGroup.style.borderColor = 'rgba(220, 53, 69, 0.8)';
        inputGroup.style.boxShadow = '0 0 0 0.2rem rgba(220, 53, 69, 0.15), 0 8px 25px rgba(220, 53, 69, 0.1)';
        
        // Add shake animation
        inputGroup.style.animation = 'shake 0.5s ease-in-out';
        setTimeout(() => {
            inputGroup.style.animation = '';
        }, 500);
    }
}

function removeErrorState(field) {
    const inputGroup = field.closest('.input-group');
    if (inputGroup) {
        inputGroup.style.borderColor = '';
        inputGroup.style.boxShadow = '';
    }
}

function showValidationMessage(message) {
    // Create or update validation message
    let messageEl = document.querySelector('.search-validation-message');
    if (!messageEl) {
        messageEl = document.createElement('div');
        messageEl.className = 'search-validation-message';
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(220, 53, 69, 0.9);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            backdrop-filter: blur(15px);
            box-shadow: 0 10px 30px rgba(220, 53, 69, 0.3);
            z-index: 9999;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        document.body.appendChild(messageEl);
    }
    
    messageEl.textContent = message;
    messageEl.style.transform = 'translateX(0)';
    
    setTimeout(() => {
        messageEl.style.transform = 'translateX(100%)';
    }, 3000);
}

/**
 * Smooth tab transitions
 */
function addTabTransitions() {
    const tabButtons = document.querySelectorAll('.search-tabs .nav-link');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Add active state animation
            this.style.transform = 'translateY(-2px) scale(1.02)';
            
            setTimeout(() => {
                this.style.transform = '';
            }, 200);
        });
    });
}

/**
 * Input focus animations
 */
function addInputAnimations() {
    const inputs = document.querySelectorAll('.search-tabs .form-control, .search-tabs .form-select');
    
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            const inputGroup = this.closest('.input-group');
            if (inputGroup) {
                inputGroup.style.transform = 'translateY(-1px)';
                inputGroup.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            }
        });
        
        input.addEventListener('blur', function() {
            const inputGroup = this.closest('.input-group');
            if (inputGroup) {
                inputGroup.style.transform = 'translateY(0)';
            }
        });
    });
}

/**
 * Button ripple effects
 */
function addButtonRippleEffects() {
    const buttons = document.querySelectorAll('.search-tabs .btn-modern, .search-tabs .btn-primary');
    
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
            `;
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

/**
 * Loading states for form submission
 */
function addLoadingStates() {
    const forms = document.querySelectorAll('.search-tabs form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitButton = this.querySelector('.btn-modern, .btn-primary');
            if (submitButton) {
                const originalText = submitButton.innerHTML;
                submitButton.innerHTML = '<i class="bi bi-arrow-clockwise spin me-2"></i>Đang tìm kiếm...';
                submitButton.disabled = true;
                
                // Re-enable after 3 seconds (in case of client-side validation failure)
                setTimeout(() => {
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;
                }, 3000);
            }
        });
    });
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }
    
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
    
    .spin {
        animation: spin 1s linear infinite;
    }
    
    .search-tabs .input-group {
        position: relative;
        overflow: visible;
    }
    
    .search-tabs .btn-modern,
    .search-tabs .btn-primary {
        position: relative;
        overflow: hidden;
    }
`;
document.head.appendChild(style);

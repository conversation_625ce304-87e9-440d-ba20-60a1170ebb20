/* Destination Showcase Styles - Based on reference design */

/* Modern Section Styles */
.section-title {
  position: relative;
  margin-bottom: 2.5rem;
  text-align: center;
}

.section-title h2 {
  font-weight: 700;
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
  font-size: 2.5rem;
  color: #333;
}

.section-title h2:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 50px;
  height: 3px;
  background-color: #4caf50;
  transform: translateX(-50%);
}

/* Modern Card Styles */
.modern-card {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: none;
  margin-bottom: 30px;
}

.modern-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.modern-card .card-img-top {
  height: 200px;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.modern-card:hover .card-img-top {
  transform: scale(1.05);
}

.modern-card .card-body {
  padding: 1.5rem;
}

.modern-card .card-title {
  font-weight: 600;
  margin-bottom: 0.75rem;
  font-size: 1.2rem;
}

.modern-card .card-text {
  color: #666;
  margin-bottom: 1.25rem;
  font-size: 0.9rem;
}

.modern-card .btn {
  border-radius: 30px;
  padding: 0.5rem 1.5rem;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.modern-card .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Modern Button Styles */
.btn-modern {
  border-radius: 30px;
  padding: 0.8rem 2rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
}

.btn-modern:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn-modern-primary {
  background-color: #4caf50;
  color: white;
}

.btn-modern-primary:hover {
  background-color: #3d8b40;
  color: white;
}

.btn-modern-outline {
  background-color: transparent;
  border: 2px solid #4caf50;
  color: #4caf50;
}

.btn-modern-outline:hover {
  background-color: #4caf50;
  color: white;
}

/* Modern Search Form */
.search-form-modern {
  background-color: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-top: -50px;
  position: relative;
  z-index: 10;
}

.search-form-modern .form-control {
  border-radius: 30px;
  padding: 0.75rem 1.25rem;
  border: 1px solid #eee;
  box-shadow: none;
  transition: all 0.3s ease;
}

.search-form-modern .form-control:focus {
  border-color: #4caf50;
  box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
}

.search-form-modern .input-group-text {
  border-radius: 30px 0 0 30px;
  background-color: white;
  border: 1px solid #eee;
  border-right: none;
}

.search-form-modern .btn {
  border-radius: 30px;
  padding: 0.75rem 2rem;
  font-weight: 500;
}

/* Main container */
#destination-showcase-container {
  position: relative;
  width: 100%;
  height: 100vh;
  min-height: 600px;
  overflow: hidden;
  margin-top: 0; /* Remove negative margin */
  padding-top: 0; /* Remove extra padding */
}

#destination-showcase-container.full-page {
  height: 100vh;
  width: 100%;
  margin: 0;
  padding: 0;
}

.destination-showcase {
  position: relative;
  width: 100%;
  height: 100vh;
  min-height: 600px;
  overflow: hidden;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  /* Ensure proper positioning with sidebar */
  margin-left: 0; /* Reset any margin since body already has margin */
}

/* Background overlay for different continents */
.destination-showcase::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.1) 100%
  );
  z-index: 1;
}

/* Background images for different provinces */
.destination-showcase.asia {
  background-image: url("/images/banners/hinh-anh-ben-tre-tho-mong-tru-tinh_022742052.jpg");
}

.destination-showcase.cantho {
  background-image: url("/images/banners/banner_home_01.jpg");
}

.destination-showcase.vinhlong {
  background-image: url("/images/banners/hinh-anh-chieu-hoang-hon-o-ben-tre_022743590.jpg");
}

.destination-showcase.tiengiang {
  background-image: url("/images/banners/banner_home_02.jpg");
}

.destination-showcase.dongthap {
  background-image: url("/images/banners/hinh-anh-trung-tam-thanh-pho-ben-tre-nhin-tu-tren-cao_022746613.jpg");
}

/* Destination container */
.destination-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 2rem;
  z-index: 2;
}

/* Header section with logo and navigation */
.showcase-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 0;
  flex-wrap: wrap;
  position: relative;
  z-index: 10;
}

.showcase-logo {
  font-size: 1.2rem;
  font-weight: 600;
  color: #fff;
  margin-right: 1rem;
}

.showcase-logo a {
  display: inline-block;
  transition: all 0.3s ease;
}

.showcase-logo a:hover {
  opacity: 0.9;
  transform: scale(1.05);
}

.showcase-nav {
  display: flex;
  gap: 1.5rem;
  flex-grow: 1;
  justify-content: center;
}

.showcase-nav-item {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 0.5rem 0;
  position: relative;
  font-weight: 500;
}

.showcase-nav-item:hover,
.showcase-nav-item.active {
  color: #fff;
}

.showcase-nav-item::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 50%;
  background-color: #fff;
  transition: width 0.3s ease;
  transform: translateX(-50%);
}

.showcase-nav-item:hover::after,
.showcase-nav-item.active::after {
  width: 30px;
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.user-actions .btn {
  transition: all 0.3s ease;
}

.user-actions .btn-outline-light {
  border-color: rgba(255, 255, 255, 0.6);
}

.user-actions .btn-outline-light:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: #fff;
}

.user-actions .btn-success {
  background-color: #4caf50;
  border-color: #4caf50;
}

.user-actions .btn-success:hover {
  background-color: #3d8b40;
  border-color: #3d8b40;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

/* Main content area */
.showcase-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 2rem; /* Add gap between left and right sections */
  min-height: 0; /* Allow flex items to shrink */
}

/* Left side with main heading */
.showcase-info {
  width: 40%;
  padding-right: 3rem; /* Increase padding to create more space */
  flex-shrink: 0; /* Prevent shrinking */
}

.continent-name {
  font-size: 5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #fff;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.continent-description {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  max-width: 90%;
  line-height: 1.6;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
}

.explore-btn {
  display: inline-block;
  padding: 0.8rem 2rem;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.explore-btn:hover {
  background-color: #3d8b40;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  color: white;
}

/* Right side with destination cards */
.showcase-destinations {
  width: 60%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: 100%;
  min-height: 500px;
  padding-left: 3rem; /* Increase padding to move content further right */
  flex-shrink: 0; /* Prevent shrinking */
  overflow: visible; /* Ensure content is not clipped */
}

.destination-card-large {
  grid-column: 1;
  grid-row: 1 / span 2;
  height: 100%;
}

.destination-card-small {
  height: 100%;
}

/* Destination card styling */
.destination-card {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.4s ease;
  cursor: pointer;
}

.destination-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);
}

.destination-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s ease;
}

.destination-card:hover img {
  transform: scale(1.08);
}

/* Vertical destination card styling */
.vertical-destination-card {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.4s ease;
  cursor: pointer;
  height: 450px;
  display: block;
  margin-bottom: 20px;
}

.vertical-destination-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);
}

.vertical-destination-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s ease;
}

.vertical-destination-card:hover img {
  transform: scale(1.08);
}

.destination-card-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 1.5rem;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.85) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0) 100%
  );
  color: white;
  transition: all 0.3s ease;
}

.destination-card:hover .destination-card-overlay {
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.9) 0%,
    rgba(0, 0, 0, 0.5) 50%,
    rgba(0, 0, 0, 0.1) 100%
  );
}

.vertical-destination-card-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 1.5rem;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.85) 0%,
    rgba(0, 0, 0, 0.6) 40%,
    rgba(0, 0, 0, 0.2) 80%,
    rgba(0, 0, 0, 0) 100%
  );
  color: white;
  transition: all 0.3s ease;
  height: 65%; /* Cover more of the card */
}

.vertical-destination-card:hover .vertical-destination-card-overlay {
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.9) 0%,
    rgba(0, 0, 0, 0.7) 40%,
    rgba(0, 0, 0, 0.3) 80%,
    rgba(0, 0, 0, 0.1) 100%
  );
}

.destination-card-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.destination-card-subtitle {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Specific styles for vertical cards */
.vertical-destination-card .destination-card-title {
  font-size: 1.8rem;
  margin-bottom: 0.75rem;
}

.vertical-destination-card .destination-card-subtitle {
  font-size: 1rem;
  margin-bottom: 1rem;
}

.vertical-destination-card .card-text {
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.vertical-destination-card .btn {
  padding: 0.6rem 1.5rem;
  font-weight: 500;
}

/* Card action buttons */
.card-actions {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
  z-index: 10;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.destination-card:hover .card-actions {
  opacity: 1;
  transform: translateY(0);
}

.card-action-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.card-action-btn:hover {
  background-color: white;
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

/* Navigation controls */
.showcase-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  position: relative;
  z-index: 10;
  margin-top: auto;
}

.continent-nav {
  display: flex;
  gap: 1rem;
  align-items: center;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.nav-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  color: white;
}

.nav-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.continent-indicators {
  display: flex;
  gap: 0.8rem;
}

.continent-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.continent-indicator.active {
  background-color: #fff;
  transform: scale(1.2);
}

/* Tab Navigation Styles */
.showcase-tab-navigation {
  position: relative;
  top: 0;
  right: 0;
  z-index: 10;
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: flex-end;
}

.tab-nav-container {
  display: flex;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.15);
  flex-wrap: wrap;
  justify-content: flex-end;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.tab-nav-btn {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
  color: rgba(255, 255, 255, 0.85);
  padding: 1rem 1.25rem;
  border-radius: 15px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.4rem;
  min-width: 90px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(15px);
}

.tab-nav-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.tab-nav-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.25);
  color: white;
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.tab-nav-btn:hover::before {
  opacity: 1;
}

.tab-nav-btn.active {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.35);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 15px 45px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.tab-nav-btn.active::before {
  opacity: 1;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15),
    rgba(255, 255, 255, 0.08)
  );
}

.tab-nav-btn i {
  font-size: 1.3rem;
  margin-bottom: 0.1rem;
  transition: transform 0.3s ease;
}

.tab-nav-btn:hover i {
  transform: scale(1.1);
}

.tab-nav-btn span {
  font-size: 0.8rem;
  font-weight: 600;
  letter-spacing: 0.3px;
  text-transform: uppercase;
}

/* Tab Content Styles */
.showcase-tab-content {
  position: relative;
  width: 100%;
  height: auto;
  min-height: 500px;
  max-height: calc(100vh - 250px);
  overflow-y: auto;
  z-index: 5;
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.tab-content-panel {
  display: none;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(25px);
  border-radius: 25px;
  padding: 2.5rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  min-height: 450px;
  position: relative;
  z-index: 10;
  flex: 1;
  margin-left: 0;
  margin-right: 0;
  overflow: hidden;
}

.tab-content-panel::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%
  );
  z-index: -1;
}

.tab-content-panel.active {
  display: flex !important;
  flex-direction: column;
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Search Form Styles in Tab */
.search-form-modern {
  background: transparent;
  margin: 0;
  padding: 0;
}

.search-tabs {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(25px);
  border-radius: 20px;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  position: relative;
  overflow: hidden;
}

.search-tabs::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%
  );
}

.search-tabs .nav-tabs {
  border: none;
  padding: 1rem;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.search-tabs .nav-link {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.85);
  border-radius: 15px;
  padding: 0.875rem 1.5rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  font-size: 0.9rem;
  backdrop-filter: blur(15px);
  margin-right: 0;
  margin-bottom: 0.5rem;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.search-tabs .nav-link::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
  transition: left 0.6s ease;
}

.search-tabs .nav-link:hover::before {
  left: 100%;
}

.search-tabs .nav-link:hover {
  background: rgba(76, 175, 80, 0.15);
  border-color: rgba(76, 175, 80, 0.3);
  color: white;
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 15px 35px rgba(76, 175, 80, 0.2),
    0 0 0 1px rgba(76, 175, 80, 0.2) inset;
}

.search-tabs .nav-link.active {
  background: linear-gradient(
    135deg,
    rgba(76, 175, 80, 0.9) 0%,
    rgba(56, 142, 60, 0.9) 100%
  );
  border-color: rgba(76, 175, 80, 0.8);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 15px 40px rgba(76, 175, 80, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.3) inset, 0 0 20px rgba(76, 175, 80, 0.3);
}

/* Search Form Content Styling */
.search-tabs .tab-content {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 18px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 1rem;
  position: relative;
  overflow: hidden;
}

.search-tabs .tab-content::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
}

.search-tabs .tab-pane {
  padding: 2rem;
}

.search-tabs .form-label {
  color: rgba(255, 255, 255, 0.95);
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.search-tabs .form-label i {
  margin-right: 0.5rem;
  font-size: 1rem;
  color: rgba(76, 175, 80, 0.8);
}

.search-tabs .input-group {
  margin-bottom: 1.5rem;
}

.search-tabs .input-group-text {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-right: none;
  color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 12px 0 0 12px;
  padding: 0.75rem 1rem;
}

.search-tabs .form-control,
.search-tabs .form-select {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-left: none;
  color: white;
  backdrop-filter: blur(10px);
  border-radius: 0 12px 12px 0;
  padding: 0.75rem 1rem;
  font-size: 0.9rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-tabs .form-control:focus,
.search-tabs .form-select:focus {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(76, 175, 80, 0.5);
  box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.15),
    0 8px 25px rgba(0, 0, 0, 0.1);
  color: white;
  transform: translateY(-1px);
}

.search-tabs .form-control::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.search-tabs .form-select option {
  background: rgba(30, 30, 30, 0.95);
  color: white;
}

.search-tabs .btn-modern {
  background: linear-gradient(
    135deg,
    rgba(76, 175, 80, 0.9) 0%,
    rgba(56, 142, 60, 0.9) 100%
  );
  border: 1px solid rgba(76, 175, 80, 0.6);
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  padding: 0.875rem 2rem;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  position: relative;
  overflow: hidden;
}

.search-tabs .btn-modern::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
  transition: left 0.6s ease;
}

.search-tabs .btn-modern:hover::before {
  left: 100%;
}

.search-tabs .btn-modern:hover {
  background: linear-gradient(
    135deg,
    rgba(76, 175, 80, 1) 0%,
    rgba(56, 142, 60, 1) 100%
  );
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 15px 35px rgba(76, 175, 80, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2) inset, 0 0 20px rgba(76, 175, 80, 0.3);
}

.search-tabs .btn-modern:active {
  transform: translateY(0) scale(0.98);
}

/* Responsive Design for Search Tabs */
@media (max-width: 768px) {
  .search-tabs {
    margin-bottom: 1.5rem;
    border-radius: 16px;
  }

  .search-tabs .nav-tabs {
    padding: 0.75rem;
    gap: 0.25rem;
  }

  .search-tabs .nav-link {
    padding: 0.75rem 1rem;
    font-size: 0.85rem;
    border-radius: 12px;
    margin-bottom: 0.25rem;
  }

  .search-tabs .tab-content {
    border-radius: 14px;
  }

  .search-tabs .tab-pane {
    padding: 1.5rem;
  }

  .search-tabs .form-label {
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
  }

  .search-tabs .input-group {
    margin-bottom: 1rem;
  }

  .search-tabs .input-group-text {
    padding: 0.625rem 0.75rem;
    border-radius: 10px 0 0 10px;
  }

  .search-tabs .form-control,
  .search-tabs .form-select {
    padding: 0.625rem 0.75rem;
    font-size: 0.85rem;
    border-radius: 0 10px 10px 0;
  }

  .search-tabs .btn-modern {
    padding: 0.75rem 1.5rem;
    font-size: 0.85rem;
    border-radius: 10px;
  }
}

@media (max-width: 576px) {
  .search-tabs .nav-tabs {
    flex-direction: column;
    align-items: stretch;
  }

  .search-tabs .nav-link {
    text-align: center;
    margin-bottom: 0.5rem;
  }

  .search-tabs .tab-pane {
    padding: 1rem;
  }

  .search-tabs .row.g-3 > * {
    margin-bottom: 1rem;
  }

  .search-tabs .btn-modern {
    width: 100%;
    margin-top: 0.5rem;
  }
}

/* Enhanced Animation Keyframes */
@keyframes searchTabFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes searchFormSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.search-tabs .tab-pane.show.active {
  animation: searchTabFadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-tabs .input-group {
  animation: searchFormSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-tabs .input-group:nth-child(2) {
  animation-delay: 0.1s;
}

.search-tabs .input-group:nth-child(3) {
  animation-delay: 0.2s;
}

.search-tabs .input-group:nth-child(4) {
  animation-delay: 0.3s;
}

/* Content Sections */
.destinations-showcase,
.hotels-showcase,
.tours-showcase,
.experiences-showcase,
.about-showcase {
  height: auto;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0;
  overflow-y: auto;
  max-height: calc(100vh - 300px);
}

/* Ensure grids and content expand to fill space */
.destinations-showcase .row,
.hotels-showcase .row,
.tours-showcase .row {
  flex: 1;
  margin-bottom: 1.5rem;
  gap: 1.5rem;
}

/* Enhanced Section Title */
.section-title {
  text-align: center;
  margin-bottom: 2.5rem;
  position: relative;
  padding-bottom: 1rem;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(76, 175, 80, 0.8) 50%,
    rgba(255, 255, 255, 0.8) 100%
  );
  border-radius: 2px;
  box-shadow: 0 2px 10px rgba(76, 175, 80, 0.3);
}

.section-title h2 {
  font-size: 2.2rem;
  margin-bottom: 0.75rem;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 800;
  text-shadow: 0 3px 15px rgba(0, 0, 0, 0.4);
  letter-spacing: -0.5px;
  line-height: 1.2;
}

.section-title p {
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 0;
  font-weight: 500;
  font-size: 1.1rem;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  line-height: 1.5;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Enhanced Card Styles in Tabs */
.destination-card-modern,
.hotel-card-modern,
.tour-card-modern {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.destination-card-modern::before,
.hotel-card-modern::before,
.tour-card-modern::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
  pointer-events: none;
}

.destination-card-modern:hover,
.hotel-card-modern:hover,
.tour-card-modern:hover {
  transform: translateY(-10px) scale(1.03);
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.2) inset;
  border-color: rgba(255, 255, 255, 0.6);
}

.destination-card-modern:hover::before,
.hotel-card-modern:hover::before,
.tour-card-modern:hover::before {
  opacity: 1;
}

/* Card Image Styling */
.destination-card-modern .card-img,
.hotel-card-modern .card-img-top,
.tour-card-modern .card-img-top {
  height: 220px;
  object-fit: cover;
  transition: transform 0.4s ease;
  position: relative;
  z-index: 2;
}

.destination-card-modern:hover .card-img,
.hotel-card-modern:hover .card-img-top,
.tour-card-modern:hover .card-img-top {
  transform: scale(1.05);
}

/* Card Body Styling */
.destination-card-modern .card-body,
.hotel-card-modern .card-body,
.tour-card-modern .card-body {
  padding: 1.75rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

.destination-card-modern .card-title,
.hotel-card-modern .card-title,
.tour-card-modern .card-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1a252f;
  margin-bottom: 0.75rem;
  line-height: 1.3;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
}

.destination-card-modern .card-text,
.hotel-card-modern .card-text,
.tour-card-modern .card-text {
  color: #2c3e50;
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 1.25rem;
  flex: 1;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0) 100%
  );
  display: flex;
  align-items: flex-end;
  padding: 1rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.destination-card-modern:hover .card-overlay {
  opacity: 1;
}

.card-content {
  color: white;
}

/* Enhanced Experience Cards */
.experience-card {
  background: rgba(255, 255, 255, 0.92);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2.5rem 2rem;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.4);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.experience-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(76, 175, 80, 0.05) 0%,
    rgba(33, 150, 243, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.experience-card:hover {
  transform: translateY(-10px) scale(1.03);
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.25);
  border-color: rgba(255, 255, 255, 0.6);
}

.experience-card:hover::before {
  opacity: 1;
}

.experience-card .mb-3 {
  position: relative;
  z-index: 2;
  margin-bottom: 1.5rem !important;
}

.experience-card i {
  transition: all 0.3s ease;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.experience-card:hover i {
  transform: scale(1.1) rotate(5deg);
  color: #27ae60 !important;
}

.experience-card h5 {
  font-size: 1.4rem;
  font-weight: 700;
  color: #1a252f;
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
}

.experience-card p {
  color: #2c3e50;
  font-size: 1rem;
  line-height: 1.6;
  position: relative;
  z-index: 2;
  margin-bottom: 0;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

/* AI Recommendation Card */
.ai-recommendation-card {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-top: 2rem;
}

/* Newsletter Section */
.newsletter-section {
  margin-top: 2rem;
}

.newsletter-card {
  background: rgba(76, 175, 80, 0.15);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  border: 1px solid rgba(76, 175, 80, 0.3);
  box-shadow: 0 10px 30px rgba(76, 175, 80, 0.1);
}

/* Info Cards */
.info-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(15px);
  border-radius: 18px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
}

.info-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
}

/* Featured Banner Styling */
.featured-destination-banner,
.featured-tour-banner,
.panorama-banner {
  margin-bottom: 2.5rem;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
  position: relative;
}

.featured-destination-banner .position-relative,
.featured-tour-banner .position-relative,
.panorama-banner .position-relative {
  border-radius: 20px;
  overflow: hidden;
}

.featured-destination-banner img,
.featured-tour-banner img {
  transition: transform 0.6s ease;
  border-radius: 20px;
}

.featured-destination-banner:hover img,
.featured-tour-banner:hover img {
  transform: scale(1.05);
}

.featured-destination-banner .position-absolute,
.featured-tour-banner .position-absolute,
.panorama-banner .position-absolute {
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.1) 100%
  );
  border-radius: 20px;
}

.featured-destination-banner h3,
.featured-tour-banner h2,
.panorama-banner h2 {
  font-weight: 800;
  text-shadow: 0 3px 15px rgba(0, 0, 0, 0.5);
  margin-bottom: 1rem;
}

.featured-destination-banner p,
.featured-tour-banner p,
.panorama-banner p {
  font-size: 1.1rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.4);
  line-height: 1.6;
}

/* Badge Styling */
.badge {
  font-size: 0.8rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Button Enhancements in Cards */
.destination-card-modern .btn,
.hotel-card-modern .btn,
.tour-card-modern .btn {
  border-radius: 12px;
  padding: 0.6rem 1.25rem;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.destination-card-modern .btn-outline-light {
  border: 2px solid rgba(255, 255, 255, 0.8);
  color: white;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.destination-card-modern .btn-outline-light:hover {
  background: rgba(255, 255, 255, 0.9);
  color: #2c3e50;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

/* Price and Meta Information Styling */
.text-success {
  color: #27ae60 !important;
  font-weight: 700;
  font-size: 1.1rem;
}

.card-text i {
  color: #3498db;
  margin-right: 0.5rem;
}

/* Grid Layout Improvements */
.destinations-showcase .row,
.hotels-showcase .row,
.tours-showcase .row {
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
}

.destinations-showcase .col-md-4,
.hotels-showcase .col-md-4,
.tours-showcase .col-md-4 {
  display: flex;
  margin-bottom: 2rem;
}

/* Enhanced Form Elements for Transparent Background */
.tab-content-panel .form-control,
.tab-content-panel .form-select {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.875rem 1.25rem;
  color: #333;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.tab-content-panel .form-control:focus,
.tab-content-panel .form-select:focus {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(76, 175, 80, 0.6);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.15),
    0 8px 25px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.tab-content-panel .form-control::placeholder {
  color: rgba(0, 0, 0, 0.5);
  font-weight: 400;
}

.tab-content-panel .form-label {
  color: rgba(255, 255, 255, 0.95);
  font-weight: 600;
  margin-bottom: 0.75rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  font-size: 0.9rem;
}

.tab-content-panel .btn-primary {
  background: linear-gradient(
    135deg,
    rgba(76, 175, 80, 0.9),
    rgba(56, 142, 60, 0.9)
  );
  backdrop-filter: blur(15px);
  border: 1px solid rgba(76, 175, 80, 0.4);
  border-radius: 15px;
  padding: 1rem 2rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-content-panel .btn-primary:hover {
  background: linear-gradient(
    135deg,
    rgba(76, 175, 80, 1),
    rgba(56, 142, 60, 1)
  );
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 15px 40px rgba(76, 175, 80, 0.4);
  border-color: rgba(76, 175, 80, 0.6);
}

.tab-content-panel .btn-outline-primary {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(76, 175, 80, 0.6);
  color: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  padding: 0.875rem 1.75rem;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-content-panel .btn-outline-primary:hover {
  background: rgba(76, 175, 80, 0.9);
  border-color: rgba(76, 175, 80, 0.8);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(76, 175, 80, 0.3);
}

/* Content Spacing and Layout Improvements */
.tab-content-panel {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.tab-content-panel::-webkit-scrollbar {
  width: 8px;
}

.tab-content-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.tab-content-panel::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  transition: background 0.3s ease;
}

.tab-content-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Content Dividers */
.content-divider {
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%
  );
  margin: 2rem 0;
}

/* Enhanced Meta Information */
.meta-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.meta-info span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #2c3e50;
  font-size: 0.9rem;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

.meta-info i {
  color: #3498db;
  font-size: 1rem;
}

/* Card Footer Enhancements */
.card-footer-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* Loading States */
.content-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: rgba(255, 255, 255, 0.8);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Empty State Styling */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: rgba(255, 255, 255, 0.8);
}

.empty-state i {
  font-size: 4rem;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 1rem;
}

.empty-state h4 {
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
}

/* About Tab Specific Styling */
.about-showcase h3 {
  color: rgba(255, 255, 255, 0.95) !important;
  font-weight: 700;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  margin-bottom: 1.5rem;
}

.about-showcase p {
  color: rgba(255, 255, 255, 0.85) !important;
  font-weight: 500;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.about-showcase .info-card h5 {
  color: #1a252f !important;
  font-weight: 700;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
}

.about-showcase .info-card p {
  color: #2c3e50 !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

.about-showcase .newsletter-card h4 {
  color: #1a252f !important;
  font-weight: 700;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
}

.about-showcase .newsletter-card p {
  color: #2c3e50 !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

/* Newsletter Section Title */
.newsletter-section .section-title h3 {
  color: rgba(255, 255, 255, 0.95) !important;
  font-weight: 700;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  font-size: 1.8rem;
  margin-bottom: 1rem;
}

/* General Text Contrast Improvements for All Tabs */
.tab-content-panel h1,
.tab-content-panel h2,
.tab-content-panel h3,
.tab-content-panel h4,
.tab-content-panel h5,
.tab-content-panel h6 {
  color: rgba(255, 255, 255, 0.95) !important;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

.tab-content-panel .lead {
  color: rgba(255, 255, 255, 0.85) !important;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
}

/* Featured Banner Text */
.featured-destination-banner .text-center h3,
.featured-tour-banner h2,
.panorama-banner h2 {
  color: white !important;
  text-shadow: 0 3px 15px rgba(0, 0, 0, 0.6);
}

.featured-destination-banner .text-center p,
.featured-tour-banner p,
.panorama-banner p {
  color: rgba(255, 255, 255, 0.95) !important;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

/* Price Text Enhancement */
.text-success {
  color: #27ae60 !important;
  font-weight: 700;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
}

/* Responsive styles */
@media (max-width: 992px) {
  .showcase-content {
    flex-direction: column;
  }

  .showcase-info,
  .showcase-destinations {
    width: 100%;
  }

  .showcase-info {
    padding-right: 0;
    margin-bottom: 2rem;
  }

  .showcase-destinations {
    padding-left: 0; /* Remove extra padding on mobile */
  }

  .continent-name {
    font-size: 3.5rem;
  }

  .showcase-tab-navigation {
    position: relative;
    top: auto;
    right: auto;
    margin-bottom: 1rem;
  }

  .tab-nav-container {
    justify-content: center;
    flex-wrap: wrap;
  }

  .tab-nav-btn {
    min-width: 75px;
    padding: 0.75rem 1rem;
    font-size: 0.75rem;
    gap: 0.3rem;
  }

  .tab-nav-btn i {
    font-size: 1.1rem;
  }

  .tab-nav-btn span {
    font-size: 0.7rem;
  }

  .showcase-tab-content {
    position: relative;
    bottom: auto;
    left: auto;
    right: auto;
    max-height: none;
    min-height: 400px;
    margin-top: 1rem;
  }

  .destinations-showcase,
  .hotels-showcase,
  .tours-showcase,
  .experiences-showcase,
  .about-showcase {
    min-height: 350px;
    padding: 0 0.5rem;
  }

  .section-title h2 {
    font-size: 1.8rem;
  }

  .section-title p {
    font-size: 1rem;
  }

  .destination-card-modern .card-body,
  .hotel-card-modern .card-body,
  .tour-card-modern .card-body {
    padding: 1.25rem;
  }

  .destination-card-modern .card-title,
  .hotel-card-modern .card-title,
  .tour-card-modern .card-title {
    font-size: 1.1rem;
  }

  .experience-card {
    padding: 1.5rem 1rem;
  }

  .experience-card h5 {
    font-size: 1.2rem;
  }

  .featured-destination-banner,
  .featured-tour-banner,
  .panorama-banner {
    margin-bottom: 1.5rem;
  }

  .meta-info {
    gap: 0.5rem;
  }

  .meta-info span {
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .showcase-content {
    padding-top: 2rem;
  }

  .showcase-destinations {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(3, 1fr);
    height: auto;
    gap: 1rem;
  }

  .destination-card-large {
    grid-column: 1;
    grid-row: 1;
    height: 250px;
  }

  .destination-card-small {
    height: 200px;
  }

  .vertical-destination-card {
    height: 400px;
  }

  .vertical-destination-card-overlay {
    height: 70%;
  }

  .continent-name {
    font-size: 3rem;
  }

  .showcase-header {
    flex-wrap: wrap;
    padding: 1rem;
  }

  .showcase-logo {
    flex: 1;
  }

  .user-actions {
    flex: 1;
    justify-content: flex-end;
  }

  .showcase-nav {
    order: 3;
    width: 100%;
    margin-top: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .user-actions .btn-success {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .dropdown-menu {
    position: absolute;
    right: 0;
    left: auto;
  }
}

@media (max-width: 576px) {
  .destination-container {
    padding: 1rem;
  }

  .continent-name {
    font-size: 2.5rem;
  }

  .showcase-nav {
    gap: 0.8rem;
  }

  .showcase-nav-item {
    font-size: 0.8rem;
  }

  .continent-description {
    max-width: 100%;
    font-size: 0.9rem;
  }

  .showcase-controls {
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
  }

  .continent-indicators {
    order: 1;
    width: 100%;
    justify-content: center;
    margin-bottom: 0.5rem;
  }

  .continent-nav {
    order: 2;
  }

  .user-actions {
    flex-wrap: wrap;
    justify-content: flex-end;
    gap: 0.5rem;
  }

  .user-actions .btn-success {
    width: 100%;
    margin-bottom: 0.5rem;
    order: -1;
  }

  .showcase-header {
    padding-bottom: 0;
  }

  .vertical-destination-card {
    height: 350px;
  }

  .vertical-destination-card-overlay {
    height: 75%;
    padding: 1rem;
  }

  .vertical-destination-card .destination-card-title {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .vertical-destination-card .card-text {
    margin-bottom: 1rem;
    font-size: 0.85rem;
  }
}

/* Responsive adjustments for sidebar layout */
@media (max-width: 768px) {
  /* Remove sidebar margin on mobile for destination showcase */
  .destination-showcase {
    margin-left: 0;
  }

  .destination-container {
    padding: 1rem;
  }

  /* Ensure proper spacing on mobile */
  .showcase-content {
    gap: 1rem;
  }

  .showcase-destinations {
    padding-left: 0;
  }
}

@media (min-width: 769px) {
  /* Ensure proper spacing for desktop with sidebar */
  .destination-showcase {
    width: 100%;
  }

  .destination-container {
    width: 100%;
    max-width: none;
  }

  /* Ensure tab content has proper spacing on desktop */
  .showcase-tab-content {
    margin-right: 2rem; /* Add right margin to prevent edge overflow */
  }

  .tab-content-panel {
    max-width: calc(100% - 2rem); /* Ensure content doesn't overflow */
  }
}

﻿<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - ViVu Travel</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet" />
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
    <link rel="stylesheet" href="~/css/style.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/animations.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/ai-loading.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/destination-showcase.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/modern-header.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/unified-style.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/sidebar-nav.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/sidebar-expandable.css" asp-append-version="true" />
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    @await RenderSectionAsync("Styles", required: false)
</head>
<body class="full-page-layout">
    <!-- AI Loading Screen -->
    <partial name="~/Views/Recommendation/_AILoadingScreen.cshtml" />

    <!-- Sidebar Navigation - Full Header Functions -->
    <div class="sidebar-nav">
        <div class="sidebar-logo">
            <a asp-controller="Home" asp-action="Index">
                <img src="/images/your-logo.png" alt="ViVu Travel" height="50">
            </a>
        </div>

        <!-- Main Navigation -->
        <div class="sidebar-section">
            <a asp-controller="Home" asp-action="Index" class="sidebar-nav-item active">
                <i class="bi bi-house-fill"></i>
                <span class="tooltip">Trang chủ</span>
            </a>
            <a asp-controller="Tour" asp-action="Index" class="sidebar-nav-item">
                <i class="bi bi-compass-fill"></i>
                <span class="tooltip">Tours</span>
            </a>
            <a asp-controller="Accommodation" asp-action="Index" class="sidebar-nav-item">
                <i class="bi bi-building-fill"></i>
                <span class="tooltip">Khách sạn</span>
            </a>
            <a asp-controller="Service" asp-action="Index" class="sidebar-nav-item">
                <i class="bi bi-stars"></i>
                <span class="tooltip">Dịch vụ</span>
            </a>
            <a asp-controller="Vehicle" asp-action="Index" class="sidebar-nav-item">
                <i class="bi bi-car-front-fill"></i>
                <span class="tooltip">Phương tiện</span>
            </a>
            <a asp-controller="About" asp-action="Index" class="sidebar-nav-item">
                <i class="bi bi-info-circle-fill"></i>
                <span class="tooltip">Giới thiệu</span>
            </a>
            <a asp-controller="Contact" asp-action="Index" class="sidebar-nav-item">
                <i class="bi bi-envelope-fill"></i>
                <span class="tooltip">Liên hệ</span>
            </a>
        </div>

        <!-- Action Buttons Section -->
        <div class="sidebar-section sidebar-actions">
            <!-- AI Expandable -->
            <div class="sidebar-expandable">
                <button class="sidebar-nav-item expandable-toggle" type="button" data-target="ai-menu">
                    <i class="bi bi-magic"></i>
                    <span class="tooltip">AI Gợi ý</span>
                    <i class="bi bi-chevron-right expand-icon"></i>
                </button>
            </div>

            <!-- Search Button -->
            <button class="sidebar-nav-item" data-bs-toggle="modal" data-bs-target="#searchModal">
                <i class="bi bi-search"></i>
                <span class="tooltip">Tìm kiếm</span>
            </button>

            <!-- Cart Expandable -->
            <div class="sidebar-expandable">
                <button class="sidebar-nav-item expandable-toggle" type="button" data-target="cart-menu">
                    <i class="bi bi-cart"></i>
                    <span class="tooltip">Giỏ hàng</span>
                    <i class="bi bi-chevron-right expand-icon"></i>
                </button>
            </div>

            <!-- User Account -->
            @if (User.Identity.IsAuthenticated)
            {
                <div class="sidebar-expandable">
                    <button class="sidebar-nav-item expandable-toggle" type="button" data-target="user-menu">
                        <i class="bi bi-person-fill"></i>
                        <span class="tooltip">Tài khoản</span>
                        <i class="bi bi-chevron-right expand-icon"></i>
                    </button>
                </div>
            }
            else
            {
                <a asp-area="Identity" asp-page="/Account/Login" class="sidebar-nav-item">
                    <i class="bi bi-person"></i>
                    <span class="tooltip">Đăng nhập</span>
                </a>
            }
        </div>

        <!-- Social Buttons -->
        <div class="sidebar-section sidebar-social">
            <a href="#" class="sidebar-nav-item social-item like">
                <i class="bi bi-heart-fill"></i>
                <span class="tooltip">Yêu thích</span>
            </a>
            <a href="#" class="sidebar-nav-item social-item share">
                <i class="bi bi-share-fill"></i>
                <span class="tooltip">Chia sẻ</span>
            </a>
        </div>
    </div>

    <!-- Expanded Sidebar Content -->
    <div class="sidebar-expanded" id="sidebar-expanded">
        <div class="sidebar-expanded-content">
            <!-- AI Menu -->
            <div class="expanded-menu" id="ai-menu">
                <div class="expanded-menu-header">
                    <h5><i class="bi bi-magic me-2"></i>AI Gợi ý</h5>
                    <button class="btn-close-expanded" data-target="ai-menu">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
                <div class="expanded-menu-body">
                    <a href="@Url.Action("SimpleForm", "Recommendation")" class="expanded-menu-item">
                        <i class="bi bi-stars me-2"></i>
                        <div>
                            <div class="item-title">Gợi Ý Lịch Trình <span class="badge bg-success ms-1">Mới</span></div>
                            <div class="item-desc">Tạo lịch trình nhanh chóng</div>
                        </div>
                    </a>
                    <a href="@Url.Action("Index", "Recommendation")" class="expanded-menu-item">
                        <i class="bi bi-list-task me-2"></i>
                        <div>
                            <div class="item-title">Gợi Ý Lịch Trình (Đầy đủ)</div>
                            <div class="item-desc">Tùy chọn chi tiết và đầy đủ</div>
                        </div>
                    </a>
                    @if (User.Identity.IsAuthenticated)
                    {
                        <a href="@Url.Action("SavedRecommendations", "Recommendation")" class="expanded-menu-item">
                            <i class="bi bi-bookmark-star me-2"></i>
                            <div>
                                <div class="item-title">Lịch Trình Đã Lưu</div>
                                <div class="item-desc">Xem các lịch trình đã lưu</div>
                            </div>
                        </a>
                    }
                </div>
            </div>

            <!-- Cart Menu -->
            <div class="expanded-menu" id="cart-menu">
                <div class="expanded-menu-header">
                    <h5><i class="bi bi-cart me-2"></i>Giỏ hàng</h5>
                    <button class="btn-close-expanded" data-target="cart-menu">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
                <div class="expanded-menu-body">
                    <a href="@Url.Action("Index", "Booking")" class="expanded-menu-item">
                        <i class="bi bi-building me-2"></i>
                        <div>
                            <div class="item-title">Giỏ đặt phòng</div>
                            <div class="item-desc">Quản lý đặt phòng khách sạn</div>
                        </div>
                    </a>
                    <a href="@Url.Action("Cart", "Tour")" class="expanded-menu-item">
                        <i class="bi bi-airplane me-2"></i>
                        <div>
                            <div class="item-title">Giỏ đặt tour</div>
                            <div class="item-desc">Quản lý đặt tour du lịch</div>
                        </div>
                    </a>
                    <a href="@Url.Action("Cart", "Service")" class="expanded-menu-item">
                        <i class="bi bi-bag me-2"></i>
                        <div>
                            <div class="item-title">Giỏ đặt dịch vụ</div>
                            <div class="item-desc">Quản lý đặt dịch vụ</div>
                        </div>
                    </a>
                    <a href="@Url.Action("Cart", "Vehicle")" class="expanded-menu-item">
                        <i class="bi bi-car-front me-2"></i>
                        <div>
                            <div class="item-title">Giỏ thuê phương tiện</div>
                            <div class="item-desc">Quản lý thuê xe</div>
                        </div>
                    </a>
                </div>
            </div>

            <!-- User Menu -->
            @if (User.Identity.IsAuthenticated)
            {
                <div class="expanded-menu" id="user-menu">
                    <div class="expanded-menu-header">
                        <h5><i class="bi bi-person-fill me-2"></i>Tài khoản</h5>
                        <button class="btn-close-expanded" data-target="user-menu">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                    <div class="expanded-menu-body">
                        <a href="@Url.Page("/Account/Manage/Index", new { area = "Identity" })" class="expanded-menu-item">
                            <i class="bi bi-gear me-2"></i>
                            <div>
                                <div class="item-title">Quản lý tài khoản</div>
                                <div class="item-desc">Cài đặt thông tin cá nhân</div>
                            </div>
                        </a>
                        <a href="@Url.Action("MyBookings", "Booking")" class="expanded-menu-item">
                            <i class="bi bi-calendar-check me-2"></i>
                            <div>
                                <div class="item-title">Đặt phòng của tôi</div>
                                <div class="item-desc">Xem lịch sử đặt phòng</div>
                            </div>
                        </a>
                        <a href="@Url.Action("MyBookings", "Tour")" class="expanded-menu-item">
                            <i class="bi bi-map me-2"></i>
                            <div>
                                <div class="item-title">Đặt tour của tôi</div>
                                <div class="item-desc">Xem lịch sử đặt tour</div>
                            </div>
                        </a>
                        @if (User.IsInRole("Admin"))
                        {
                            <div class="expanded-menu-divider"></div>
                            <a href="/Admin" class="expanded-menu-item">
                                <i class="bi bi-shield-check me-2"></i>
                                <div>
                                    <div class="item-title">Quản lý hệ thống</div>
                                    <div class="item-desc">Truy cập trang quản trị</div>
                                </div>
                            </a>
                        }
                        <div class="expanded-menu-divider"></div>
                        <a href="@Url.Page("/Account/Logout", new { area = "Identity" })" class="expanded-menu-item text-danger">
                            <i class="bi bi-box-arrow-right me-2"></i>
                            <div>
                                <div class="item-title">Đăng xuất</div>
                                <div class="item-desc">Thoát khỏi tài khoản</div>
                            </div>
                        </a>
                    </div>
                </div>
            }
        </div>
    </div>

    <div class="wrapper full-width">


        <div class="content-wrapper w-100">
            <main role="main" class="pb-3 w-100">
                <div class="fade-in">
                    @RenderBody()
                </div>
            </main>
        </div>

        <footer class="footer mt-5 py-5 bg-dark text-white">
            <div class="container-fluid px-3 px-md-5">
                <div class="row">
                    <div class="col-md-3 mb-4">
                        <h5 class="mb-3">ViVu Travel</h5>
                        <p>Đặt phòng khách sạn trực tuyến với giá tốt nhất. Chúng tôi cung cấp dịch vụ đặt phòng khách sạn, tour du lịch và các dịch vụ khác tại các điểm du lịch nổi tiếng trên khắp Việt Nam.</p>
                        <div class="d-flex gap-3 mt-3 social-links">
                            <a href="#" class="text-white"><i class="bi bi-facebook fs-5 pulse"></i></a>
                            <a href="#" class="text-white"><i class="bi bi-instagram fs-5 pulse"></i></a>
                            <a href="#" class="text-white"><i class="bi bi-twitter fs-5 pulse"></i></a>
                            <a href="#" class="text-white"><i class="bi bi-youtube fs-5 pulse"></i></a>
                        </div>
                    </div>
                    <div class="col-md-2 mb-4">
                        <h5 class="mb-3">Dịch vụ</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2"><a asp-controller="Accommodation" asp-action="Index" class="text-white text-decoration-none">Khách sạn</a></li>
                            <li class="mb-2"><a asp-controller="Tour" asp-action="Index" class="text-white text-decoration-none">Tour du lịch</a></li>
                            <li class="mb-2"><a asp-controller="Service" asp-action="Index" class="text-white text-decoration-none">Dịch vụ</a></li>
                            <li class="mb-2"><a asp-controller="Vehicle" asp-action="Index" class="text-white text-decoration-none">Phương tiện</a></li>
                            <li class="mb-2"><a asp-controller="Combo" asp-action="Index" class="text-white text-decoration-none">Combo tiết kiệm</a></li>
                            <li class="mb-2"><a asp-controller="Panorama360" asp-action="Index" class="text-white text-decoration-none">Du lịch 360°</a></li>
                            <li class="mb-2"><a asp-controller="Recommendation" asp-action="SimpleForm" class="text-white text-decoration-none">AI Gợi Ý Lịch Trình</a></li>
                        </ul>
                    </div>
                    <div class="col-md-2 mb-4">
                        <h5 class="mb-3">Điểm đến</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2"><a href="#" class="text-white text-decoration-none">Bến Tre</a></li>
                            <li class="mb-2"><a href="#" class="text-white text-decoration-none">Cần Thơ</a></li>
                            <li class="mb-2"><a href="#" class="text-white text-decoration-none">Vĩnh Long</a></li>
                            <li class="mb-2"><a href="#" class="text-white text-decoration-none">Tiền Giang</a></li>
                            <li class="mb-2"><a href="#" class="text-white text-decoration-none">Đồng Tháp</a></li>
                            <li class="mb-2"><a href="#" class="text-white text-decoration-none">Hồ Chí Minh</a></li>
                            <li class="mb-2"><a href="#" class="text-white text-decoration-none">Xem tất cả</a></li>
                        </ul>
                    </div>
                    <div class="col-md-2 mb-4">
                        <h5 class="mb-3">Thông tin</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2"><a asp-controller="Home" asp-action="Index" class="text-white text-decoration-none">Trang chủ</a></li>
                            <li class="mb-2"><a asp-controller="Home" asp-action="About" class="text-white text-decoration-none">Giới thiệu</a></li>
                            <li class="mb-2"><a asp-controller="Home" asp-action="Contact" class="text-white text-decoration-none">Liên hệ</a></li>
                            <li class="mb-2"><a asp-controller="Home" asp-action="Privacy" class="text-white text-decoration-none">Chính sách bảo mật</a></li>
                            <li class="mb-2"><a href="#" class="text-white text-decoration-none">Điều khoản sử dụng</a></li>
                            <li class="mb-2"><a href="#" class="text-white text-decoration-none">Câu hỏi thường gặp</a></li>
                            <li class="mb-2"><a href="#" class="text-white text-decoration-none">Hướng dẫn đặt tour</a></li>
                        </ul>
                    </div>
                    <div class="col-md-3 mb-4">
                        <h5 class="mb-3">Liên hệ</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2"><i class="bi bi-geo-alt me-2"></i> 123 Nguyễn Huệ, Quận 1, TP.HCM</li>
                            <li class="mb-2"><i class="bi bi-telephone me-2"></i> (028) 1234 5678</li>
                            <li class="mb-2"><i class="bi bi-envelope me-2"></i> <EMAIL></li>
                            <li class="mb-2"><i class="bi bi-clock me-2"></i> 8:00 - 17:30, Thứ 2 - Thứ 6</li>
                        </ul>
                        <h5 class="mb-3 mt-4">Đăng ký nhận tin</h5>
                        <div class="input-group">
                            <input type="email" class="form-control" placeholder="Email của bạn">
                            <button class="btn btn-primary" type="button">Đăng ký</button>
                        </div>
                    </div>
                </div>
                <hr class="my-4" />
                <div class="row">
                    <div class="col-md-6 text-center text-md-start">
                        <p class="mb-0">&copy; @DateTime.Now.Year - ViVu Travel - Đặt phòng khách sạn và tour du lịch trực tuyến</p>
                    </div>
                    <div class="col-md-6 text-center text-md-end">
                        <img src="/images/payment-methods.png" alt="Phương thức thanh toán" height="30" onerror="this.style.display='none'">
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Search Modal -->
    <div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="searchModalLabel">Tìm kiếm</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="/Search" method="get" class="modern-form">
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" placeholder="Nhập từ khóa tìm kiếm..." name="q" required>
                            <button class="btn btn-modern btn-modern-primary" type="submit">
                                <i class="bi bi-search me-1"></i> Tìm
                            </button>
                        </div>
                        <div class="d-flex flex-wrap gap-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="tours" id="searchTours" name="type" checked>
                                <label class="form-check-label" for="searchTours">
                                    Tours
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="hotels" id="searchHotels" name="type" checked>
                                <label class="form-check-label" for="searchHotels">
                                    Khách sạn
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="destinations" id="searchDestinations" name="type" checked>
                                <label class="form-check-label" for="searchDestinations">
                                    Địa điểm
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/js/ai-loading.js" asp-append-version="true"></script>
    <script src="~/js/destination-showcase.js" asp-append-version="true"></script>
    <script src="~/js/homepage-tabs.js" asp-append-version="true"></script>
    <script src="~/js/search-enhancements.js" asp-append-version="true"></script>
    <!-- Temporarily disable conflicting scripts -->
    <!-- <script src="~/js/sidebar-nav.js" asp-append-version="true"></script> -->
    <!-- <script src="~/js/sidebar-expandable.js" asp-append-version="true"></script> -->
    <!-- <script src="~/js/sidebar-simple-test.js" asp-append-version="true"></script> -->
    <script>
        // Initialize AOS (Animate On Scroll)
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: false,
            mirror: false,
            offset: 100,
            delay: 50,
            anchorPlacement: 'top-bottom'
        });

        // Add smooth scrolling to all links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                if (targetId === '#') return;

                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Sidebar navigation and mobile menu toggle
        document.addEventListener('DOMContentLoaded', function() {

            // Sidebar navigation active state (inline version)
            function updateSidebarActiveState() {
                const currentPath = window.location.pathname.toLowerCase();
                const sidebarItems = document.querySelectorAll('.sidebar-nav-item:not(.dropdown-toggle):not(.social-item)');

                sidebarItems.forEach(item => {
                    item.classList.remove('active');
                    const href = item.getAttribute('href');

                    if (href) {
                        const itemPath = href.toLowerCase();

                        // Check for exact match or controller match
                        if (currentPath === itemPath ||
                            (currentPath.includes('/home') && itemPath.includes('/home')) ||
                            (currentPath.includes('/destination') && itemPath.includes('/destination')) ||
                            (currentPath.includes('/accommodation') && itemPath.includes('/accommodation')) ||
                            (currentPath.includes('/tour') && itemPath.includes('/tour')) ||
                            (currentPath.includes('/service') && itemPath.includes('/service')) ||
                            (currentPath.includes('/vehicle') && itemPath.includes('/vehicle')) ||
                            (currentPath.includes('/panorama360') && itemPath.includes('/panorama360')) ||
                            (currentPath.includes('/recommendation') && itemPath.includes('/recommendation')) ||
                            (currentPath.includes('/about') && itemPath.includes('/about')) ||
                            (currentPath.includes('/contact') && itemPath.includes('/contact')) ||
                            (currentPath === '/' && itemPath.includes('/home'))) {
                            item.classList.add('active');
                        }
                    }
                });

                // Handle dropdown active states
                const dropdownButtons = document.querySelectorAll('.sidebar-dropdown .dropdown-toggle');
                dropdownButtons.forEach(button => {
                    button.classList.remove('active');
                    const dropdownMenu = button.nextElementSibling;
                    if (dropdownMenu) {
                        const dropdownItems = dropdownMenu.querySelectorAll('.dropdown-item');
                        dropdownItems.forEach(dropdownItem => {
                            const href = dropdownItem.getAttribute('href');
                            if (href && currentPath.includes(href.toLowerCase().split('/')[1])) {
                                button.classList.add('active');
                            }
                        });
                    }
                });
            }

            // Update sidebar on page load
            updateSidebarActiveState();

            // Sidebar dropdown functionality is handled by sidebar-dropdown.js
            // This ensures no conflicts with Bootstrap dropdown

            // Inline expandable sidebar functionality
            function initExpandableSidebar() {
                console.log('Initializing inline expandable sidebar...');

                const expandableButtons = document.querySelectorAll('.expandable-toggle');
                const sidebarExpanded = document.getElementById('sidebar-expanded');

                console.log('Found:', {
                    buttons: expandableButtons.length,
                    sidebar: !!sidebarExpanded
                });

                expandableButtons.forEach((button, index) => {
                    console.log(`Setting up button ${index + 1}:`, button);

                    button.addEventListener('click', function(e) {
                        console.log('Button clicked!', this);
                        e.preventDefault();
                        e.stopPropagation();

                        const target = this.getAttribute('data-target');
                        const targetMenu = document.getElementById(target);

                        console.log('Target:', target, 'Menu:', targetMenu);

                        if (sidebarExpanded && targetMenu) {
                            // Hide all menus
                            document.querySelectorAll('.expanded-menu').forEach(menu => {
                                menu.classList.remove('active');
                            });

                            // Toggle sidebar
                            if (sidebarExpanded.classList.contains('show')) {
                                sidebarExpanded.classList.remove('show');
                                console.log('Sidebar hidden');
                            } else {
                                sidebarExpanded.classList.add('show');
                                targetMenu.classList.add('active');
                                console.log('Sidebar shown');
                            }
                        }
                    });
                });
            }

            // Initialize expandable sidebar
            initExpandableSidebar();

            // Add close button functionality
            document.querySelectorAll('.btn-close-expanded').forEach(button => {
                button.addEventListener('click', function(e) {
                    console.log('Close button clicked');
                    e.preventDefault();
                    e.stopPropagation();

                    const sidebarExpanded = document.getElementById('sidebar-expanded');
                    if (sidebarExpanded) {
                        sidebarExpanded.classList.remove('show');
                        document.querySelectorAll('.expanded-menu').forEach(menu => {
                            menu.classList.remove('active');
                        });
                        console.log('Sidebar closed');
                    }
                });
            });

            // Add overlay click to close
            document.addEventListener('click', function(e) {
                const sidebarExpanded = document.getElementById('sidebar-expanded');
                const sidebar = document.querySelector('.sidebar-nav');

                if (sidebarExpanded && sidebarExpanded.classList.contains('show')) {
                    if (!sidebarExpanded.contains(e.target) && !sidebar.contains(e.target)) {
                        sidebarExpanded.classList.remove('show');
                        document.querySelectorAll('.expanded-menu').forEach(menu => {
                            menu.classList.remove('active');
                        });
                        console.log('Sidebar closed by outside click');
                    }
                }
            });

            // Add search modal functionality
            const searchModal = document.getElementById('searchModal');
            if (searchModal) {
                searchModal.addEventListener('shown.bs.modal', function () {
                    searchModal.querySelector('input').focus();
                });
            }
        });
    </script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
